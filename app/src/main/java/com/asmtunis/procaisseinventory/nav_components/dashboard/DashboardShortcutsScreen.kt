package com.asmtunis.procaisseinventory.nav_components.dashboard

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Refresh
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.asmtunis.procaisseinventory.R
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.Globals.DEVICE_ID
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.navigation.ProCaisseSyncRoute
import com.asmtunis.procaisseinventory.core.navigation.ProInventorySyncRoute
import com.asmtunis.procaisseinventory.core.sync.SyncCompletionViewModel
import com.asmtunis.procaisseinventory.core.utils.PackageUtils
import com.asmtunis.procaisseinventory.core.utils.Sync.getProCaisseTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getProInventoryTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.getSharedTotalNoSyncCount
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProCaisse
import com.asmtunis.procaisseinventory.core.utils.Sync.syncAllProInventory
import com.asmtunis.procaisseinventory.nav_components.NavigationDrawerViewModel
import com.asmtunis.procaisseinventory.nav_components.data.model.MenuModel
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.shared_ui_components.ToastKMM
import com.asmtunis.procaisseinventory.shared_ui_components.showToast
import com.asmtunis.procaisseinventory.view_model.MainViewModel
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.dokar.sonner.ToastType
import com.dokar.sonner.rememberToasterState

@Composable
fun DashboardShortcutsScreen(
    navigate: (route: Any) -> Unit,
    onMenuClick: (menu: MenuModel) -> Unit,
    menus: List<MenuModel>,
    mainViewModel: MainViewModel,
    navDrawerVM: NavigationDrawerViewModel,
    dataViewModel: DataViewModel,
    onLogoutClick: () -> Unit,
    onSyncClick: () -> Unit,
    isConnected: Boolean = true,
    isSyncing: Boolean = false,
    syncCompletionViewModel: SyncCompletionViewModel? = null,
    syncProcaisseViewModels: SyncProcaisseViewModels? = null,
    syncInventoryViewModel: SyncInventoryViewModel? = null,
    syncSharedViewModels: SyncSharedViewModels? = null,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val androidId = remember { DEVICE_ID }
    val toaster = rememberToasterState()
    val utilisateur: Utilisateur = mainViewModel.utilisateur
    val exerciceCode = dataViewModel.getExercice()

    // Toast component for copy feedback
    ToastKMM(toaster = toaster, darkTheme = true)
    
    // Filter menus based on authorization (same logic as drawer)
    val filteredMenus = remember(menus, mainViewModel.clientList, utilisateur.autorisationUser) {
        menus.filterIndexed { index, menu ->
            navDrawerVM.addMenu(
                menus = menus,
                index = index,
                clientList = mainViewModel.clientList,
                authorizationList = utilisateur.autorisationUser.filter { it.AutEtat == "1" }
            )
        }
    }
    
    // Check if sync is complete
    val isSyncComplete = syncCompletionViewModel?.isSyncComplete != false

    // Calculate sync count based on current mode
    val noSyncCount = remember(syncProcaisseViewModels, syncInventoryViewModel, syncSharedViewModels, navDrawerVM.isProInventory) {
        val sharedCount = syncSharedViewModels?.let { getSharedTotalNoSyncCount(it) } ?: 0

        if (navDrawerVM.isProInventory) {
            val inventoryCount = syncInventoryViewModel?.let { getProInventoryTotalNoSyncCount(it) } ?: 0
            sharedCount + inventoryCount
        } else {
            val proCaisseCount = syncProcaisseViewModels?.let { getProCaisseTotalNoSyncCount(it) } ?: 0
            sharedCount + proCaisseCount
        }
    }

    // Define colors for cards (similar to your image)
    val cardColors = listOf(
        Color(0xFF6B73FF), // Blue-purple
        Color(0xFFFF6B6B), // Red
        Color(0xFFFF9F43), // Orange
        Color(0xFF4ECDC4), // Teal
        Color(0xFF45B7D1), // Light blue
        Color(0xFF96CEB4), // Green
        Color(0xFFFECA57), // Yellow
        Color(0xFFFF6B9D), // Pink
        Color(0xFF786FA6), // Purple
        Color(0xFF4834D4)  // Dark purple
    )

    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(top = 27.dp) // Reduced since DashboardUserHeader now has its own top padding
        ) {
        // User header
        DashboardUserHeader(
            utilisateur = utilisateur,
            stationList = mainViewModel.stationList,
            logo = mainViewModel.logo,
            exerciceCode = exerciceCode,
            onLogoutClick = onLogoutClick,
            onSyncClick = onSyncClick,
            isConnected = isConnected,
            isSyncing = isSyncing
        )
        
        Spacer(modifier = Modifier.height(24.dp))

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "Menu",
                style = MaterialTheme.typography.headlineSmall.copy(
                    fontWeight = FontWeight.Bold,
                    fontSize = 25.sp
                ),
                color = MaterialTheme.colorScheme.onBackground,
                textAlign = TextAlign.Center
            )
        }

        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Sync status message if not complete
        if (!isSyncComplete) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.errorContainer
                )
            ) {
                Text(
                    text = stringResource(R.string.sync_in_progress_message),
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onErrorContainer,
                    modifier = Modifier.padding(16.dp),
                    textAlign = TextAlign.Center
                )
            }
            
            Spacer(modifier = Modifier.height(16.dp))
        }
        
        // Menu cards in grid - using regular Column/Row layout for unified scrolling
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Create rows of 2 items each
            filteredMenus.chunked(2).forEach { rowMenus ->
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    rowMenus.forEach { menu ->
                        val colorIndex = filteredMenus.indexOf(menu) % cardColors.size
                        DashboardCard(
                            menu = menu,
                            onClick = {
                                if (isSyncComplete) {
                                    // Add debug logging to see which menu is clicked
                                    android.util.Log.d("DashboardCard", "Clicked menu: ${menu.id} - ${context.getString(menu.title)}")
                                    onMenuClick(menu)
                                }
                            },
                            backgroundColor = cardColors[colorIndex],
                            enabled = isSyncComplete,
                            actionCount = if (menu.badge.isNotEmpty()) menu.badge else "",
                            modifier = Modifier.weight(1f)
                        )
                    }

                    // If the row has only one item, add a spacer to maintain layout
                    if (rowMenus.size == 1) {
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Device ID and App Version (centered after shortcuts)
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Device ID (copyable)
            Text(
                modifier = Modifier
                    .clickable {
                        showToast(
                            context = context,
                            toaster = toaster,
                            message = AnnotatedString(androidId).text + "\n" + context.resources.getString(R.string.Copied),
                            type = ToastType.Success,
                        )
                        clipboardManager.setText(AnnotatedString(androidId))
                    },
                text = AnnotatedString(androidId),
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            // App Version
            Text(
                text = PackageUtils.getVersionName(context = context),
                fontSize = MaterialTheme.typography.bodySmall.fontSize,
                textAlign = TextAlign.Center
            )
        }

            // Add bottom spacing for better scrolling experience
            Spacer(modifier = Modifier.height(80.dp))
        }

        // Floating Action Button for sync - positioned at bottom right (always visible)
        FloatingActionButton(
            onClick = {
                // Call sync functions directly like in dashboard screen
                if (navDrawerVM.isProInventory) {
                    // Sync ProInventory data (shared + inventory)
                    if (syncInventoryViewModel != null && syncSharedViewModels != null) {
                        val sharedCount = getSharedTotalNoSyncCount(syncSharedViewModels)
                        val inventoryCount = getProInventoryTotalNoSyncCount(syncInventoryViewModel)

                        if (sharedCount + inventoryCount > 0) {
                            syncAllProInventory(
                                syncSharedViewModels = syncSharedViewModels,
                                syncInventoryViewModel = syncInventoryViewModel
                            )
                        }
                    }
                } else {
                    // Sync ProCaisse data (shared + procaisse)
                    if (syncProcaisseViewModels != null && syncSharedViewModels != null) {
                        val sharedCount = getSharedTotalNoSyncCount(syncSharedViewModels)
                        val proCaisseCount = getProCaisseTotalNoSyncCount(syncProcaisseViewModels)

                        if (sharedCount + proCaisseCount > 0) {
                            syncAllProCaisse(
                                syncProcaisseViewModels = syncProcaisseViewModels
                            )
                        }
                    }
                }
            },
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(16.dp),
            containerColor = MaterialTheme.colorScheme.primary
        ) {
            if (noSyncCount > 0) {
                BadgedBox(
                    badge = {
                        Badge {
                            Text(
                                text = noSyncCount.toString(),
                                style = MaterialTheme.typography.labelSmall
                            )
                        }
                    }
                ) {
                    Icon(
                        imageVector = Icons.Filled.Refresh,
                        contentDescription = stringResource(id = R.string.sync_title),
                        tint = MaterialTheme.colorScheme.onPrimary
                    )
                }
            } else {
                Icon(
                    imageVector = Icons.Filled.Refresh,
                    contentDescription = stringResource(id = R.string.sync_title),
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
        }
    }
}
