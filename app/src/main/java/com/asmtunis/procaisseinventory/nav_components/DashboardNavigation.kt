package com.asmtunis.procaisseinventory.nav_components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.launch
import com.asmtunis.procaisseinventory.auth.login.data.domaine.Utilisateur
import com.asmtunis.procaisseinventory.core.local_storage.datastore.viewmodel.DataViewModel
import com.asmtunis.procaisseinventory.core.connectivity.internet.NetworkViewModel
import com.asmtunis.procaisseinventory.nav_components.dashboard.DashboardShortcutsScreen
import com.asmtunis.procaisseinventory.nav_components.sync.SyncLoadingOverlay
import com.asmtunis.procaisseinventory.pro_inventory.sync.SyncInventoryViewModel
import com.asmtunis.procaisseinventory.setting.SettingViewModel
import com.asmtunis.procaisseinventory.view_model.SyncSharedViewModels
import com.asmtunis.procaisseinventory.view_model.SyncProcaisseViewModels
import com.asmtunis.procaisseinventory.view_model.GetProCaisseDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetProInventoryDataViewModel
import com.asmtunis.procaisseinventory.view_model.GetSharedDataViewModel
import com.asmtunis.procaisseinventory.view_model.MainViewModel

@Composable
fun DashboardNavigation(
    navigate: (route: Any) -> Unit,
    mainViewModel: MainViewModel,
    navDrawerViewmodel: NavigationDrawerViewModel,
    getProCaisseDataViewModel: GetProCaisseDataViewModel,
    getProInventoryDataViewModel: GetProInventoryDataViewModel,
    getSharedDataViewModel: GetSharedDataViewModel,
    networkViewModel: NetworkViewModel,
    dataViewModel: DataViewModel,
    syncInventoryViewModel: SyncInventoryViewModel,
    syncSharedViewModels: SyncSharedViewModels,
    syncProcaisseViewModels: SyncProcaisseViewModels,
    settingViewModel: SettingViewModel,
    onLogoutClick: () -> Unit,
    syncCompletionViewModel: com.asmtunis.procaisseinventory.core.sync.SyncCompletionViewModel? = null
) {
    val menus =
        if (navDrawerViewmodel.isProInventory) navDrawerViewmodel.proInventoryListMenu
        else navDrawerViewmodel.proCaisseListMenu

    val utilisateur = mainViewModel.utilisateur
    val isConnected = networkViewModel.isConnected

    // Check if any sync operation is in progress
    val isSyncing = remember(
        getSharedDataViewModel.stationState.loading,
        getSharedDataViewModel.parametragesState.loading,
        getProCaisseDataViewModel.clientsState.loading,
        getProInventoryDataViewModel.typePrixUnitaireHTState.loading
    ) {
        getSharedDataViewModel.stationState.loading ||
        getSharedDataViewModel.parametragesState.loading ||
        getProCaisseDataViewModel.clientsState.loading ||
        getProInventoryDataViewModel.typePrixUnitaireHTState.loading
    }

    // Check sync completion status (only if syncCompletionViewModel is provided)
    syncCompletionViewModel?.let { syncVM ->
        LaunchedEffect(utilisateur) {
            if (utilisateur != Utilisateur()) {
                syncVM.checkSyncCompletion(
                    getSharedDataViewModel = getSharedDataViewModel,
                    getProCaisseDataViewModel = getProCaisseDataViewModel,
                    getProInventoryDataViewModel = getProInventoryDataViewModel,
                    utilisateur = utilisateur
                )
            }
        }

        // Continuously monitor sync status
        LaunchedEffect(
            getSharedDataViewModel.stationState.loading,
            getSharedDataViewModel.parametragesState.loading,
            getProCaisseDataViewModel.clientsState.loading,
            getProInventoryDataViewModel.typePrixUnitaireHTState.loading
        ) {
            syncVM.checkSyncCompletion(
                getSharedDataViewModel = getSharedDataViewModel,
                getProCaisseDataViewModel = getProCaisseDataViewModel,
                getProInventoryDataViewModel = getProInventoryDataViewModel,
                utilisateur = utilisateur
            )
        }
    }

    Box {
        // Main dashboard content
        DashboardShortcutsScreen(
            navigate = navigate,
            onMenuClick = { menu ->
                // Only allow navigation if sync is complete (or if syncCompletionViewModel is not provided)
                if (syncCompletionViewModel?.isSyncComplete != false) {
                    navDrawerViewmodel.onSelectedMenuChange(menu)
                    navigate(menu.route)
                }
            },
            menus = menus,
            mainViewModel = mainViewModel,
            navDrawerVM = navDrawerViewmodel,
            dataViewModel = dataViewModel,
            onLogoutClick = onLogoutClick,
            onSyncClick = {
                // Trigger sync based on current mode
                if (navDrawerViewmodel.isProInventory) {
                    navigate(com.asmtunis.procaisseinventory.core.navigation.ProInventorySyncRoute)
                } else {
                    navigate(com.asmtunis.procaisseinventory.core.navigation.ProCaisseSyncRoute)
                }
            },
            isConnected = isConnected,
            isSyncing = isSyncing,
            syncCompletionViewModel = syncCompletionViewModel,
            syncProcaisseViewModels = syncProcaisseViewModels,
            syncInventoryViewModel = syncInventoryViewModel,
            syncSharedViewModels = syncSharedViewModels
        )

        // Show sync loading overlay if sync is not complete (only if syncCompletionViewModel is provided)
        syncCompletionViewModel?.let { syncVM ->
            SyncLoadingOverlay(
                syncCompletionViewModel = syncVM
            )
        }
    }
}
